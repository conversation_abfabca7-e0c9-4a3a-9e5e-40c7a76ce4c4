import { Fish, Position, Velocity } from '../../types';
import { PHYSICS, GAME_CONFIG } from '../../utils/constants';
import { constrainToBounds, bounceOffBounds, magnitude } from '../../utils/math';

class MovementSystem {
  update(fishes: Fish[], deltaTime: number, worldWidth: number, worldHeight: number): void {
    fishes.forEach((fish) => {
      this.updateFishMovement(fish, deltaTime, worldWidth, worldHeight);
    });
  }

  private updateFishMovement(
    fish: Fish,
    deltaTime: number,
    worldWidth: number,
    worldHeight: number
  ): void {
    // Skip if fish is not alive
    if (!fish.isAlive) return;

    // Apply velocity to position
    const newPosition: Position = {
      x: fish.position.x + fish.velocity.x * deltaTime,
      y: fish.position.y + fish.velocity.y * deltaTime,
    };

    // Handle world boundaries
    if (this.isOutOfBounds(newPosition, fish.radius, worldWidth, worldHeight)) {
      // Bounce off boundaries for AI fish
      if (!fish.isPlayer) {
        fish.velocity = bounceOffBounds(
          fish.position,
          fish.velocity,
          fish.radius,
          worldWidth,
          worldHeight
        );

        // Apply bounce damping
        fish.velocity.x *= PHYSICS.BOUNCE_DAMPING;
        fish.velocity.y *= PHYSICS.BOUNCE_DAMPING;
      }

      // Constrain position to bounds
      fish.position = constrainToBounds(newPosition, fish.radius, worldWidth, worldHeight);
    } else {
      fish.position = newPosition;
    }

    // Apply AI movement for non-player fish
    if (!fish.isPlayer) {
      this.updateAIMovement(fish, deltaTime);
    }

    // Apply friction
    fish.velocity.x *= PHYSICS.FRICTION;
    fish.velocity.y *= PHYSICS.FRICTION;

    // Update last update timestamp
    fish.lastUpdate = Date.now();
  }

  private updateAIMovement(fish: Fish, deltaTime: number): void {
    // Simple AI: occasionally change direction
    const changeDirectionChance = 0.02 * deltaTime; // 2% chance per second

    if (Math.random() < changeDirectionChance) {
      // Generate new random direction
      const angle = Math.random() * Math.PI * 2;
      const speed = GAME_CONFIG.enemy.speedRange[0] +
        Math.random() * (GAME_CONFIG.enemy.speedRange[1] - GAME_CONFIG.enemy.speedRange[0]);

      fish.velocity = {
        x: Math.cos(angle) * speed,
        y: Math.sin(angle) * speed,
      };
    }

    // Ensure AI fish don't move too fast or too slow
    const currentSpeed = magnitude(fish.velocity);
    const minSpeed = GAME_CONFIG.enemy.speedRange[0];
    const maxSpeed = GAME_CONFIG.enemy.speedRange[1];

    if (currentSpeed < minSpeed && currentSpeed > 0) {
      const scale = minSpeed / currentSpeed;
      fish.velocity.x *= scale;
      fish.velocity.y *= scale;
    } else if (currentSpeed > maxSpeed) {
      const scale = maxSpeed / currentSpeed;
      fish.velocity.x *= scale;
      fish.velocity.y *= scale;
    }

    // Prefer horizontal movement (as per PRD)
    fish.velocity.y *= 0.7;
  }

  private isOutOfBounds(
    position: Position,
    radius: number,
    worldWidth: number,
    worldHeight: number
  ): boolean {
    return (
      position.x - radius < 0 ||
      position.x + radius > worldWidth ||
      position.y - radius < 0 ||
      position.y + radius > worldHeight
    );
  }

  // Helper method to calculate fish speed based on size
  getSpeedForSize(sizeLevel: number): number {
    const baseSpeed = GAME_CONFIG.player.baseSpeed;
    const speedDecay = GAME_CONFIG.player.speedDecayFactor;
    return baseSpeed * Math.pow(speedDecay, sizeLevel - 1);
  }

  // Helper method to apply inertia to movement
  applyInertia(currentVelocity: Velocity, targetVelocity: Velocity, deltaTime: number): Velocity {
    const inertiaFactor = 0.1; // How quickly fish responds to input changes

    return {
      x: currentVelocity.x + (targetVelocity.x - currentVelocity.x) * inertiaFactor * deltaTime,
      y: currentVelocity.y + (targetVelocity.y - currentVelocity.y) * inertiaFactor * deltaTime,
    };
  }
}

export default MovementSystem;
