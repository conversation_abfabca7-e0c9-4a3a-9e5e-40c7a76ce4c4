import React, { useRef, useCallback } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import {
  PanGesture<PERSON>and<PERSON>,
  PanGestureHandlerGestureEvent,
  State,
} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedG<PERSON>ureHandler,
  useAnimatedStyle,
  useSharedValue,
  runOnJS,
  withSpring,
} from 'react-native-reanimated';
import { JoystickInput } from '../types';
import { UI_CONSTANTS, INPUT, COLORS } from '../utils/constants';
import { clamp, magnitude } from '../utils/math';

interface Props {
  onInputChange: (input: JoystickInput) => void;
  style?: any;
}

const { width, height } = Dimensions.get('window');

const VirtualJoystick: React.FC<Props> = ({ onInputChange, style }) => {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);

  const joystickRadius = UI_CONSTANTS.JOYSTICK_SIZE / 2;
  const knobRadius = UI_CONSTANTS.JOYSTICK_KNOB_SIZE / 2;
  const maxDistance = joystickRadius - knobRadius;

  const updateInput = useCallback((x: number, y: number) => {
    const distance = magnitude({ x, y });
    const normalizedDistance = Math.min(distance / maxDistance, 1);

    const input: JoystickInput = {
      x: distance > INPUT.JOYSTICK_DEADZONE ? x / maxDistance : 0,
      y: distance > INPUT.JOYSTICK_DEADZONE ? y / maxDistance : 0,
      magnitude: normalizedDistance,
      isBoosting: normalizedDistance >= INPUT.BOOST_THRESHOLD,
    };

    onInputChange(input);
  }, [maxDistance, onInputChange]);

  const gestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent>({
    onStart: () => {
      scale.value = withSpring(1.1);
    },
    onActive: (event) => {
      const { translationX, translationY } = event;

      // Calculate distance from center
      const distance = Math.sqrt(translationX * translationX + translationY * translationY);

      if (distance <= maxDistance) {
        translateX.value = translationX;
        translateY.value = translationY;
      } else {
        // Constrain to circle boundary
        const angle = Math.atan2(translationY, translationX);
        translateX.value = Math.cos(angle) * maxDistance;
        translateY.value = Math.sin(angle) * maxDistance;
      }

      // Update input on JS thread
      runOnJS(updateInput)(translateX.value, translateY.value);
    },
    onEnd: () => {
      // Return to center
      translateX.value = withSpring(0);
      translateY.value = withSpring(0);
      scale.value = withSpring(1);

      // Reset input
      runOnJS(updateInput)(0, 0);
    },
  });

  const knobAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
      ],
    };
  });

  const baseAnimatedStyle = useAnimatedStyle(() => {
    const inputMagnitude = Math.sqrt(translateX.value * translateX.value + translateY.value * translateY.value) / maxDistance;
    const opacity = 0.3 + (inputMagnitude * 0.4);

    return {
      opacity,
      transform: [{ scale: scale.value * 0.95 + 0.05 }],
    };
  });

  return (
    <View style={[styles.container, style]}>
      <PanGestureHandler onGestureEvent={gestureHandler}>
        <Animated.View style={styles.joystickContainer}>
          {/* Base circle */}
          <Animated.View style={[styles.joystickBase, baseAnimatedStyle]} />

          {/* Center dot */}
          <View style={styles.centerDot} />

          {/* Knob */}
          <Animated.View style={[styles.joystickKnob, knobAnimatedStyle]} />
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 40,
    left: 40,
    width: UI_CONSTANTS.JOYSTICK_SIZE,
    height: UI_CONSTANTS.JOYSTICK_SIZE,
  },
  joystickContainer: {
    width: UI_CONSTANTS.JOYSTICK_SIZE,
    height: UI_CONSTANTS.JOYSTICK_SIZE,
    justifyContent: 'center',
    alignItems: 'center',
  },
  joystickBase: {
    position: 'absolute',
    width: UI_CONSTANTS.JOYSTICK_SIZE,
    height: UI_CONSTANTS.JOYSTICK_SIZE,
    borderRadius: UI_CONSTANTS.JOYSTICK_SIZE / 2,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.4)',
  },
  centerDot: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
  },
  joystickKnob: {
    width: UI_CONSTANTS.JOYSTICK_KNOB_SIZE,
    height: UI_CONSTANTS.JOYSTICK_KNOB_SIZE,
    borderRadius: UI_CONSTANTS.JOYSTICK_KNOB_SIZE / 2,
    backgroundColor: COLORS.PLAYER,
    borderWidth: 3,
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default VirtualJoystick;
