import { Fish, FoodParticle } from '../../types';
import { circleCollision, distance } from '../../utils/math';
import { PHYSICS } from '../../utils/constants';

interface CollisionCallbacks {
  onFishEaten: (predatorId: string, preyId: string) => void;
  onFoodEaten: (fishId: string, foodId: string) => void;
  onPlayerDeath: (fishId: string) => void;
}

class CollisionSystem {
  update(
    fishes: Fish[],
    foodParticles: FoodParticle[],
    callbacks: CollisionCallbacks
  ): void {
    // Check fish-to-fish collisions
    this.checkFishCollisions(fishes, callbacks);

    // Check fish-to-food collisions
    this.checkFoodCollisions(fishes, foodParticles, callbacks);
  }

  private checkFishCollisions(fishes: Fish[], callbacks: CollisionCallbacks): void {
    for (let i = 0; i < fishes.length; i++) {
      const fishA = fishes[i];
      if (!fishA.isAlive) continue;

      for (let j = i + 1; j < fishes.length; j++) {
        const fishB = fishes[j];
        if (!fishB.isAlive) continue;

        // Check if fish are colliding
        if (this.areFishColliding(fishA, fishB)) {
          this.handleFishCollision(fishA, fishB, callbacks);
        }
      }
    }
  }

  private checkFoodCollisions(
    fishes: Fish[],
    foodParticles: FoodParticle[],
    callbacks: CollisionCallbacks
  ): void {
    fishes.forEach((fish) => {
      if (!fish.isAlive) return;

      foodParticles.forEach((food) => {
        if (this.isFishCollidingWithFood(fish, food)) {
          callbacks.onFoodEaten(fish.id, food.id);
        }
      });
    });
  }

  private areFishColliding(fishA: Fish, fishB: Fish): boolean {
    return circleCollision(
      fishA.position,
      fishA.radius,
      fishB.position,
      fishB.radius
    );
  }

  private isFishCollidingWithFood(fish: Fish, food: FoodParticle): boolean {
    return circleCollision(
      fish.position,
      fish.radius,
      food.position,
      food.radius
    );
  }

  private handleFishCollision(
    fishA: Fish,
    fishB: Fish,
    callbacks: CollisionCallbacks
  ): void {
    // Determine predator and prey based on size levels
    if (fishA.sizeLevel > fishB.sizeLevel) {
      // Fish A eats Fish B
      this.processFishEating(fishA, fishB, callbacks);
    } else if (fishB.sizeLevel > fishA.sizeLevel) {
      // Fish B eats Fish A
      this.processFishEating(fishB, fishA, callbacks);
    } else {
      // Same size - bounce off each other
      this.handleSameSizeBounce(fishA, fishB);
    }
  }

  private processFishEating(
    predator: Fish,
    prey: Fish,
    callbacks: CollisionCallbacks
  ): void {
    // Mark prey as dead
    prey.isAlive = false;

    // Trigger eating callback
    callbacks.onFishEaten(predator.id, prey.id);

    // If prey was a player, trigger death callback
    if (prey.isPlayer) {
      callbacks.onPlayerDeath(prey.id);
    }
  }

  private handleSameSizeBounce(fishA: Fish, fishB: Fish): void {
    // Calculate collision normal
    const dx = fishB.position.x - fishA.position.x;
    const dy = fishB.position.y - fishA.position.y;
    const dist = distance(fishA.position, fishB.position);

    if (dist === 0) return; // Avoid division by zero

    // Normalize collision vector
    const nx = dx / dist;
    const ny = dy / dist;

    // Separate fish to prevent overlap
    const overlap = (fishA.radius + fishB.radius) - dist;
    const separationDistance = overlap / 2 + PHYSICS.COLLISION_PRECISION;

    fishA.position.x -= nx * separationDistance;
    fishA.position.y -= ny * separationDistance;
    fishB.position.x += nx * separationDistance;
    fishB.position.y += ny * separationDistance;

    // Calculate relative velocity
    const relativeVelocityX = fishA.velocity.x - fishB.velocity.x;
    const relativeVelocityY = fishA.velocity.y - fishB.velocity.y;

    // Calculate relative velocity along collision normal
    const velocityAlongNormal = relativeVelocityX * nx + relativeVelocityY * ny;

    // Don't resolve if velocities are separating
    if (velocityAlongNormal > 0) return;

    // Calculate restitution (bounciness)
    const restitution = PHYSICS.BOUNCE_DAMPING;

    // Calculate impulse scalar
    const impulseScalar = -(1 + restitution) * velocityAlongNormal;

    // Assume equal mass for simplicity
    const impulse = impulseScalar / 2;

    // Apply impulse to velocities
    fishA.velocity.x += impulse * nx;
    fishA.velocity.y += impulse * ny;
    fishB.velocity.x -= impulse * nx;
    fishB.velocity.y -= impulse * ny;
  }

  // Helper method to check if a fish is within eating range of another
  canEat(predator: Fish, prey: Fish): boolean {
    if (!predator.isAlive || !prey.isAlive) return false;
    if (predator.sizeLevel <= prey.sizeLevel) return false;

    const eatDistance = predator.radius + prey.radius;
    const actualDistance = distance(predator.position, prey.position);

    return actualDistance <= eatDistance;
  }

  // Helper method to get all fish within a certain radius
  getFishInRadius(
    centerFish: Fish,
    allFish: Fish[],
    radius: number
  ): Fish[] {
    return allFish.filter((fish) => {
      if (fish.id === centerFish.id || !fish.isAlive) return false;
      return distance(centerFish.position, fish.position) <= radius;
    });
  }

  // Helper method to get all food within a certain radius
  getFoodInRadius(
    centerFish: Fish,
    allFood: FoodParticle[],
    radius: number
  ): FoodParticle[] {
    return allFood.filter((food) => {
      return distance(centerFish.position, food.position) <= radius;
    });
  }
}

export default CollisionSystem;
