import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ImageBackground,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../types';
import { COLORS } from '../utils/constants';

type GameOverScreenNavigationProp = StackNavigationProp<RootStackParamList, 'GameOver'>;
type GameOverScreenRouteProp = RouteProp<RootStackParamList, 'GameOver'>;

interface Props {
  navigation: GameOverScreenNavigationProp;
  route: GameOverScreenRouteProp;
}

const { width, height } = Dimensions.get('window');

const GameOverScreen: React.FC<Props> = ({ navigation, route }) => {
  const { finalScore } = route.params;

  const handlePlayAgain = () => {
    navigation.navigate('Game');
  };

  const handleMainMenu = () => {
    navigation.navigate('MainMenu');
  };

  const formatScore = (score: number): string => {
    if (score >= 1000000) {
      return `${(score / 1000000).toFixed(1)}M`;
    } else if (score >= 1000) {
      return `${(score / 1000).toFixed(1)}K`;
    }
    return score.toString();
  };

  const getScoreRating = (score: number): { title: string; message: string; color: string } => {
    if (score >= 10000) {
      return {
        title: 'Ocean Predator!',
        message: 'You dominated the seas!',
        color: '#DC2626',
      };
    } else if (score >= 5000) {
      return {
        title: 'Apex Fish!',
        message: 'You ruled your territory!',
        color: '#EA580C',
      };
    } else if (score >= 2000) {
      return {
        title: 'Big Fish!',
        message: 'You grew impressively large!',
        color: '#7C3AED',
      };
    } else if (score >= 500) {
      return {
        title: 'Growing Fish!',
        message: 'You made good progress!',
        color: '#2563EB',
      };
    } else {
      return {
        title: 'Small Fry',
        message: 'Keep practicing to grow bigger!',
        color: '#10B981',
      };
    }
  };

  const rating = getScoreRating(finalScore);

  return (
    <View style={styles.container}>
      <ImageBackground
        source={{ uri: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojMUU0MEFGO3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiMwRjE3MkE7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiIC8+Cjwvc3ZnPgo=' }}
        style={styles.background}
        resizeMode="cover"
      >
        <View style={styles.overlay}>
          <View style={styles.content}>
            {/* Game Over Title */}
            <View style={styles.titleContainer}>
              <Text style={styles.gameOverText}>Game Over</Text>
              <View style={styles.divider} />
            </View>

            {/* Score Section */}
            <View style={styles.scoreContainer}>
              <Text style={styles.scoreLabel}>Final Score</Text>
              <Text style={styles.scoreValue}>{formatScore(finalScore)}</Text>
              <Text style={styles.scoreExact}>({finalScore.toLocaleString()} points)</Text>
            </View>

            {/* Rating Section */}
            <View style={styles.ratingContainer}>
              <View style={[styles.ratingBadge, { backgroundColor: rating.color }]}>
                <Text style={styles.ratingTitle}>{rating.title}</Text>
              </View>
              <Text style={styles.ratingMessage}>{rating.message}</Text>
            </View>

            {/* Stats Section */}
            <View style={styles.statsContainer}>
              <Text style={styles.statsTitle}>Performance</Text>
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{Math.floor(finalScore / 100)}</Text>
                  <Text style={styles.statLabel}>Fish Eaten</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statValue}>{Math.min(20, Math.floor(finalScore / 500) + 1)}</Text>
                  <Text style={styles.statLabel}>Max Level</Text>
                </View>
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity style={styles.primaryButton} onPress={handlePlayAgain}>
                <Text style={styles.primaryButtonText}>Play Again</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.secondaryButton} onPress={handleMainMenu}>
                <Text style={styles.secondaryButtonText}>Main Menu</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ImageBackground>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  content: {
    width: Math.min(400, width * 0.9),
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
    padding: 30,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  gameOverText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#EF4444',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  divider: {
    width: 100,
    height: 3,
    backgroundColor: '#EF4444',
    marginTop: 10,
    borderRadius: 2,
  },
  scoreContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  scoreLabel: {
    fontSize: 18,
    color: '#E5E7EB',
    marginBottom: 8,
  },
  scoreValue: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  scoreExact: {
    fontSize: 14,
    color: '#9CA3AF',
    marginTop: 4,
  },
  ratingContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  ratingBadge: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    marginBottom: 12,
  },
  ratingTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  ratingMessage: {
    fontSize: 16,
    color: '#E5E7EB',
    textAlign: 'center',
  },
  statsContainer: {
    width: '100%',
    marginBottom: 30,
  },
  statsTitle: {
    fontSize: 18,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
    fontWeight: '600',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.PLAYER,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
  },
  buttonContainer: {
    width: '100%',
    gap: 16,
  },
  primaryButton: {
    backgroundColor: COLORS.PLAYER,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  primaryButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  secondaryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default GameOverScreen;
