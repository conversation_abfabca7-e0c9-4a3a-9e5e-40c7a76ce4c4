import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import Animated, { FadeInUp, FadeOutUp } from 'react-native-reanimated';
import { LeaderboardEntry } from '../types';
import { UI_CONSTANTS, COLORS } from '../utils/constants';

interface Props {
  leaderboard: LeaderboardEntry[];
  currentPlayerId?: string;
  style?: any;
}

const Leaderboard: React.FC<Props> = ({ leaderboard, currentPlayerId, style }) => {
  const formatScore = (score: number): string => {
    if (score >= 1000000) {
      return `${(score / 1000000).toFixed(1)}M`;
    } else if (score >= 1000) {
      return `${(score / 1000).toFixed(1)}K`;
    }
    return score.toString();
  };

  const getRankColor = (rank: number): string => {
    switch (rank) {
      case 1:
        return '#FFD700'; // Gold
      case 2:
        return '#C0C0C0'; // Silver
      case 3:
        return '#CD7F32'; // Bronze
      default:
        return '#E5E7EB'; // Default
    }
  };

  const displayedEntries = leaderboard.slice(0, UI_CONSTANTS.LEADERBOARD_MAX_ENTRIES);

  return (
    <View style={[styles.container, style]}>
      <View style={styles.header}>
        <Text style={styles.title}>Leaderboard</Text>
      </View>

      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {displayedEntries.map((entry, index) => (
          <Animated.View
            key={entry.playerId}
            entering={FadeInUp.delay(index * 100)}
            exiting={FadeOutUp}
            style={[
              styles.entryContainer,
              entry.playerId === currentPlayerId && styles.currentPlayerEntry,
            ]}
          >
            <View style={styles.rankContainer}>
              <Text style={[styles.rankText, { color: getRankColor(entry.rank) }]}>
                #{entry.rank}
              </Text>
            </View>

            <View style={styles.playerInfo}>
              <Text
                style={[
                  styles.playerName,
                  entry.playerId === currentPlayerId && styles.currentPlayerName,
                ]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {entry.playerName}
              </Text>
              <Text style={styles.playerScore}>
                {formatScore(entry.score)}
              </Text>
            </View>
          </Animated.View>
        ))}

        {displayedEntries.length === 0 && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No players yet</Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 40,
    right: 20,
    width: UI_CONSTANTS.LEADERBOARD_WIDTH,
    maxHeight: 400,
    backgroundColor: COLORS.UI_OVERLAY,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    overflow: 'hidden',
  },
  header: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  title: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 4,
  },
  entryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.05)',
  },
  currentPlayerEntry: {
    backgroundColor: 'rgba(16, 185, 129, 0.2)',
    borderLeftWidth: 3,
    borderLeftColor: COLORS.PLAYER,
  },
  rankContainer: {
    width: 30,
    alignItems: 'center',
  },
  rankText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  playerInfo: {
    flex: 1,
    marginLeft: 8,
  },
  playerName: {
    fontSize: 12,
    color: '#E5E7EB',
    fontWeight: '600',
    marginBottom: 2,
  },
  currentPlayerName: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  playerScore: {
    fontSize: 11,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  emptyContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 12,
    color: '#9CA3AF',
    fontStyle: 'italic',
  },
});

export default Leaderboard;
