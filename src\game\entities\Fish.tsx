import React from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useAnimatedStyle,
  withSpring,
  interpolateColor,
} from 'react-native-reanimated';
import Svg, { Circle, Path } from 'react-native-svg';
import { Fish as FishType, FishOutlineColor } from '../../types';
import { COLORS } from '../../utils/constants';

interface Props {
  fish: FishType;
  outlineColor?: FishOutlineColor;
  isVisible?: boolean;
  scale?: number;
}

const FishEntity: React.FC<Props> = ({
  fish,
  outlineColor = 'none',
  isVisible = true,
  scale = 1
}) => {
  const fishSize = fish.radius * 2 * scale;

  const getOutlineColor = (color: FishOutlineColor): string => {
    switch (color) {
      case 'green':
        return COLORS.OUTLINE.EDIBLE;
      case 'red':
        return COLORS.OUTLINE.DANGEROUS;
      case 'yellow':
        return COLORS.OUTLINE.SIMILAR;
      default:
        return 'transparent';
    }
  };

  const animatedStyle = useAnimatedStyle(() => {
    const opacity = isVisible ? 1 : 0;
    const fishScale = fish.isBoosting ? withSpring(1.1) : withSpring(1);

    return {
      opacity: withSpring(opacity),
      transform: [
        { translateX: fish.position.x - fishSize / 2 },
        { translateY: fish.position.y - fishSize / 2 },
        { scale: fishScale },
      ],
    };
  });

  const outlineStyle = useAnimatedStyle(() => {
    const strokeColor = getOutlineColor(outlineColor);
    const strokeWidth = outlineColor !== 'none' ? 3 : 0;

    return {
      borderColor: strokeColor,
      borderWidth: strokeWidth,
    };
  });

  // Create fish shape path
  const createFishPath = (size: number): string => {
    const width = size;
    const height = size * 0.7;
    const centerX = width / 2;
    const centerY = height / 2;

    // Fish body (ellipse-like shape)
    const bodyWidth = width * 0.6;
    const bodyHeight = height * 0.8;

    // Tail
    const tailWidth = width * 0.4;
    const tailHeight = height * 0.6;

    return `
      M ${centerX - bodyWidth/2} ${centerY}
      Q ${centerX - bodyWidth/2} ${centerY - bodyHeight/2} ${centerX} ${centerY - bodyHeight/2}
      Q ${centerX + bodyWidth/2} ${centerY - bodyHeight/2} ${centerX + bodyWidth/2} ${centerY}
      Q ${centerX + bodyWidth/2} ${centerY + bodyHeight/2} ${centerX} ${centerY + bodyHeight/2}
      Q ${centerX - bodyWidth/2} ${centerY + bodyHeight/2} ${centerX - bodyWidth/2} ${centerY}
      L ${centerX + bodyWidth/2} ${centerY}
      L ${centerX + bodyWidth/2 + tailWidth} ${centerY - tailHeight/2}
      L ${centerX + bodyWidth/2 + tailWidth * 0.7} ${centerY}
      L ${centerX + bodyWidth/2 + tailWidth} ${centerY + tailHeight/2}
      L ${centerX + bodyWidth/2} ${centerY}
      Z
    `;
  };

  // Boost effect particles
  const renderBoostEffect = () => {
    if (!fish.isBoosting) return null;

    return (
      <View style={styles.boostEffect}>
        {[...Array(6)].map((_, index) => (
          <Animated.View
            key={index}
            style={[
              styles.boostParticle,
              {
                transform: [
                  { rotate: `${index * 60}deg` },
                  { translateX: fishSize * 0.3 },
                ],
              },
            ]}
          />
        ))}
      </View>
    );
  };

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <Animated.View style={[styles.fishContainer, outlineStyle]}>
        <Svg width={fishSize} height={fishSize} style={styles.svg}>
          {/* Fish body */}
          <Path
            d={createFishPath(fishSize)}
            fill={fish.color}
            stroke="rgba(0, 0, 0, 0.2)"
            strokeWidth={1}
          />

          {/* Eye */}
          <Circle
            cx={fishSize * 0.35}
            cy={fishSize * 0.35}
            r={fishSize * 0.08}
            fill="#FFFFFF"
          />
          <Circle
            cx={fishSize * 0.37}
            cy={fishSize * 0.35}
            r={fishSize * 0.04}
            fill="#000000"
          />

          {/* Fins */}
          <Path
            d={`M ${fishSize * 0.3} ${fishSize * 0.6}
                Q ${fishSize * 0.2} ${fishSize * 0.8} ${fishSize * 0.35} ${fishSize * 0.75}
                Q ${fishSize * 0.4} ${fishSize * 0.7} ${fishSize * 0.3} ${fishSize * 0.6}`}
            fill={fish.color}
            fillOpacity={0.8}
          />
        </Svg>

        {/* Player name label */}
        {fish.isPlayer && fish.playerName && (
          <View style={styles.nameLabel}>
            <Animated.Text style={styles.nameText}>
              {fish.playerName}
            </Animated.Text>
          </View>
        )}

        {/* Boost effect */}
        {renderBoostEffect()}
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
  },
  fishContainer: {
    borderRadius: 1000, // Large radius for circular outline
    overflow: 'visible',
  },
  svg: {
    overflow: 'visible',
  },
  nameLabel: {
    position: 'absolute',
    top: -25,
    left: '50%',
    transform: [{ translateX: -50 }],
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 40,
    alignItems: 'center',
  },
  nameText: {
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  boostEffect: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -50 }, { translateY: -50 }],
  },
  boostParticle: {
    position: 'absolute',
    width: 4,
    height: 4,
    backgroundColor: '#FCD34D',
    borderRadius: 2,
    opacity: 0.8,
  },
});

export default FishEntity;
