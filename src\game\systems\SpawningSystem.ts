import { Fish, FoodParticle, GameWorld, Position } from '../../types';
import { GAME_CONFIG, FISH_SIZE_LEVELS, COLORS, BALANCE } from '../../utils/constants';
import {
  generateId,
  randomEdgePosition,
  randomPosition,
  randomVelocity,
  distance,
  randomElement
} from '../../utils/math';

class SpawningSystem {
  private lastEnemySpawn: number = 0;
  private lastFoodSpawn: number = 0;

  update(
    world: GameWorld,
    addFish: (fish: Fish) => void,
    addFoodParticle: (food: FoodParticle) => void,
    deltaTime: number
  ): void {
    const currentTime = Date.now();

    // Spawn enemy fish
    if (this.shouldSpawnEnemy(currentTime, world.fishes.length)) {
      const enemyFish = this.createEnemyFish(world);
      if (enemyFish) {
        addFish(enemyFish);
        this.lastEnemySpawn = currentTime;
      }
    }

    // Spawn food particles
    if (this.shouldSpawnFood(currentTime, world.foodParticles.length)) {
      const foodParticle = this.createFoodParticle(world);
      if (foodParticle) {
        addFoodParticle(foodParticle);
        this.lastFoodSpawn = currentTime;
      }
    }
  }

  private shouldSpawnEnemy(currentTime: number, currentEnemyCount: number): boolean {
    const timeSinceLastSpawn = currentTime - this.lastEnemySpawn;
    const hasSpaceForMore = currentEnemyCount < GAME_CONFIG.enemy.maxCount;
    const timeToSpawn = timeSinceLastSpawn >= GAME_CONFIG.enemy.spawnRate;

    return hasSpaceForMore && timeToSpawn;
  }

  private shouldSpawnFood(currentTime: number, currentFoodCount: number): boolean {
    const timeSinceLastSpawn = currentTime - this.lastFoodSpawn;
    const hasSpaceForMore = currentFoodCount < GAME_CONFIG.food.maxCount;
    const timeToSpawn = timeSinceLastSpawn >= GAME_CONFIG.food.spawnRate;

    return hasSpaceForMore && timeToSpawn;
  }

  private createEnemyFish(world: GameWorld): Fish | null {
    // Get player fish to determine appropriate spawn location and size
    const playerFish = world.fishes.find(fish => fish.isPlayer);
    if (!playerFish) return null;

    // Choose a random size level (weighted towards smaller fish)
    const sizeLevel = this.getRandomEnemySizeLevel(playerFish.sizeLevel);
    const sizeConfig = FISH_SIZE_LEVELS[sizeLevel - 1];

    // Find a spawn position that's not too close to the player
    const spawnPosition = this.findValidSpawnPosition(
      world,
      playerFish.position,
      sizeConfig.radius
    );

    if (!spawnPosition) return null;

    // Create enemy fish
    const enemyFish: Fish = {
      id: generateId(),
      position: spawnPosition,
      velocity: randomVelocity(
        GAME_CONFIG.enemy.speedRange[0],
        GAME_CONFIG.enemy.speedRange[1]
      ),
      sizeLevel,
      radius: sizeConfig.radius,
      color: sizeConfig.color,
      isPlayer: false,
      score: 0,
      growthProgress: Math.random() * sizeConfig.fishNeeded,
      fishNeededToGrow: sizeConfig.fishNeeded,
      isBoosting: false,
      isAlive: true,
      lastUpdate: Date.now(),
    };

    return enemyFish;
  }

  private createFoodParticle(world: GameWorld): FoodParticle | null {
    // Find a random position in the world
    const position = randomPosition(world.width, world.height);

    // Make sure it's not too close to any large fish
    const tooCloseToFish = world.fishes.some(fish => {
      const dist = distance(position, fish.position);
      return dist < fish.radius + 50; // 50 pixel buffer
    });

    if (tooCloseToFish) return null;

    const foodParticle: FoodParticle = {
      id: generateId(),
      position,
      radius: 3 + Math.random() * 2, // Random size between 3-5
      color: COLORS.FOOD,
      nutritionValue: GAME_CONFIG.food.nutritionValue,
    };

    return foodParticle;
  }

  private getRandomEnemySizeLevel(playerSizeLevel: number): number {
    // Create a weighted distribution around the player's size level
    const weights: number[] = [];
    const maxLevel = Math.min(FISH_SIZE_LEVELS.length, playerSizeLevel + 3);

    for (let level = 1; level <= maxLevel; level++) {
      if (level < playerSizeLevel) {
        // Smaller fish - higher probability
        weights.push(3);
      } else if (level === playerSizeLevel) {
        // Same size - medium probability
        weights.push(2);
      } else {
        // Larger fish - lower probability
        weights.push(1);
      }
    }

    // Select based on weights
    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let random = Math.random() * totalWeight;

    for (let i = 0; i < weights.length; i++) {
      random -= weights[i];
      if (random <= 0) {
        return i + 1;
      }
    }

    return 1; // Fallback
  }

  private findValidSpawnPosition(
    world: GameWorld,
    playerPosition: Position,
    fishRadius: number
  ): Position | null {
    const maxAttempts = 10;

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      // Try spawning from edge first, then random position
      const position = attempt < 5
        ? randomEdgePosition(world.width, world.height)
        : randomPosition(world.width, world.height);

      // Check distance from player
      const distanceFromPlayer = distance(position, playerPosition);
      if (distanceFromPlayer < BALANCE.SPAWN_DISTANCES.MIN_FROM_PLAYER) {
        continue;
      }

      // Check if position is valid (not overlapping with other fish)
      const overlapping = world.fishes.some(fish => {
        const dist = distance(position, fish.position);
        return dist < fish.radius + fishRadius + 10; // 10 pixel buffer
      });

      if (!overlapping) {
        return position;
      }
    }

    return null; // Couldn't find valid position
  }

  // Helper method to clean up dead entities
  cleanupDeadEntities(world: GameWorld): void {
    // Remove dead fish (this would be called from the main game loop)
    world.fishes = world.fishes.filter(fish => fish.isAlive);
  }

  // Helper method to get spawn statistics
  getSpawnStats(world: GameWorld): {
    enemyCount: number;
    foodCount: number;
    canSpawnEnemy: boolean;
    canSpawnFood: boolean;
  } {
    const currentTime = Date.now();

    return {
      enemyCount: world.fishes.filter(fish => !fish.isPlayer).length,
      foodCount: world.foodParticles.length,
      canSpawnEnemy: this.shouldSpawnEnemy(currentTime, world.fishes.length),
      canSpawnFood: this.shouldSpawnFood(currentTime, world.foodParticles.length),
    };
  }
}

export default SpawningSystem;
