import { Camera, Fish, Position } from '../../types';
import { lerp, clamp } from '../../utils/math';

class CameraSystem {
  private smoothingFactor: number = 0.1;
  private zoomSmoothingFactor: number = 0.05;

  update(
    camera: Camera,
    targetFish: Fish | null,
    screenWidth: number,
    screenHeight: number
  ): void {
    if (!targetFish) return;

    // Update camera position to follow target
    this.updateCameraPosition(camera, targetFish, screenWidth, screenHeight);

    // Update camera zoom based on fish size
    this.updateCameraZoom(camera, targetFish);
  }

  private updateCameraPosition(
    camera: Camera,
    targetFish: Fish,
    screenWidth: number,
    screenHeight: number
  ): void {
    // Calculate target camera position (center the fish on screen)
    const targetX = targetFish.position.x - screenWidth / 2;
    const targetY = targetFish.position.y - screenHeight / 2;

    // Smoothly interpolate to target position
    camera.position.x = lerp(camera.position.x, targetX, this.smoothingFactor);
    camera.position.y = lerp(camera.position.y, targetY, this.smoothingFactor);
  }

  private updateCameraZoom(camera: Camera, targetFish: Fish): void {
    // Calculate target zoom based on fish size
    // Larger fish = zoomed out more to see more of the world
    const baseZoom = 1.0;
    const zoomFactor = 0.05; // How much zoom changes per size level
    const targetZoom = Math.max(0.5, baseZoom - (targetFish.sizeLevel - 1) * zoomFactor);

    // Smoothly interpolate to target zoom
    camera.zoom = lerp(camera.zoom, targetZoom, this.zoomSmoothingFactor);
  }

  // Helper method to convert world coordinates to screen coordinates
  worldToScreen(
    worldPosition: Position,
    camera: Camera,
    screenWidth: number,
    screenHeight: number
  ): Position {
    return {
      x: (worldPosition.x - camera.position.x) * camera.zoom,
      y: (worldPosition.y - camera.position.y) * camera.zoom,
    };
  }

  // Helper method to convert screen coordinates to world coordinates
  screenToWorld(
    screenPosition: Position,
    camera: Camera,
    screenWidth: number,
    screenHeight: number
  ): Position {
    return {
      x: screenPosition.x / camera.zoom + camera.position.x,
      y: screenPosition.y / camera.zoom + camera.position.y,
    };
  }

  // Helper method to check if a world position is visible on screen
  isPositionVisible(
    worldPosition: Position,
    camera: Camera,
    screenWidth: number,
    screenHeight: number,
    buffer: number = 100
  ): boolean {
    const screenPos = this.worldToScreen(worldPosition, camera, screenWidth, screenHeight);

    return (
      screenPos.x >= -buffer &&
      screenPos.x <= screenWidth + buffer &&
      screenPos.y >= -buffer &&
      screenPos.y <= screenHeight + buffer
    );
  }

  // Helper method to get the visible world bounds
  getVisibleWorldBounds(
    camera: Camera,
    screenWidth: number,
    screenHeight: number
  ): {
    left: number;
    right: number;
    top: number;
    bottom: number;
  } {
    const topLeft = this.screenToWorld({ x: 0, y: 0 }, camera, screenWidth, screenHeight);
    const bottomRight = this.screenToWorld(
      { x: screenWidth, y: screenHeight },
      camera,
      screenWidth,
      screenHeight
    );

    return {
      left: topLeft.x,
      right: bottomRight.x,
      top: topLeft.y,
      bottom: bottomRight.y,
    };
  }

  // Helper method to set camera smoothing factors
  setSmoothingFactors(positionSmoothing: number, zoomSmoothing: number): void {
    this.smoothingFactor = clamp(positionSmoothing, 0.01, 1.0);
    this.zoomSmoothingFactor = clamp(zoomSmoothing, 0.01, 1.0);
  }

  // Helper method to instantly snap camera to target (no smoothing)
  snapToTarget(
    camera: Camera,
    targetFish: Fish,
    screenWidth: number,
    screenHeight: number
  ): void {
    camera.position.x = targetFish.position.x - screenWidth / 2;
    camera.position.y = targetFish.position.y - screenHeight / 2;

    const baseZoom = 1.0;
    const zoomFactor = 0.05;
    camera.zoom = Math.max(0.5, baseZoom - (targetFish.sizeLevel - 1) * zoomFactor);
  }
}

export default CameraSystem;
