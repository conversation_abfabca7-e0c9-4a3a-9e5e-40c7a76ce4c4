import React from 'react';
import { StyleSheet } from 'react-native';
import Animated, {
  useAnimatedStyle,
  withRepeat,
  withTiming,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import Svg, { Circle, Defs, RadialGradient, Stop } from 'react-native-svg';
import { FoodParticle as FoodParticleType } from '../../types';

interface Props {
  foodParticle: FoodParticleType;
  isVisible?: boolean;
  scale?: number;
}

const FoodParticleEntity: React.FC<Props> = ({
  foodParticle,
  isVisible = true,
  scale = 1
}) => {
  const particleSize = foodParticle.radius * 2 * scale;
  const pulseScale = useSharedValue(1);
  const glowOpacity = useSharedValue(0.5);

  // Start pulsing animation
  React.useEffect(() => {
    pulseScale.value = withRepeat(
      withTiming(1.2, { duration: 1500 }),
      -1,
      true
    );

    glowOpacity.value = withRepeat(
      withTiming(0.8, { duration: 2000 }),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    const opacity = isVisible ? 1 : 0;

    return {
      opacity: withSpring(opacity),
      transform: [
        { translateX: foodParticle.position.x - particleSize / 2 },
        { translateY: foodParticle.position.y - particleSize / 2 },
        { scale: pulseScale.value },
      ],
    };
  });

  const glowStyle = useAnimatedStyle(() => {
    return {
      opacity: glowOpacity.value,
    };
  });

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      {/* Glow effect */}
      <Animated.View style={[styles.glowContainer, glowStyle]}>
        <Svg width={particleSize * 2} height={particleSize * 2} style={styles.glowSvg}>
          <Defs>
            <RadialGradient id="glowGradient" cx="50%" cy="50%" r="50%">
              <Stop offset="0%" stopColor={foodParticle.color} stopOpacity="0.6" />
              <Stop offset="70%" stopColor={foodParticle.color} stopOpacity="0.2" />
              <Stop offset="100%" stopColor={foodParticle.color} stopOpacity="0" />
            </RadialGradient>
          </Defs>
          <Circle
            cx={particleSize}
            cy={particleSize}
            r={particleSize * 0.8}
            fill="url(#glowGradient)"
          />
        </Svg>
      </Animated.View>

      {/* Main particle */}
      <Svg width={particleSize} height={particleSize} style={styles.svg}>
        <Defs>
          <RadialGradient id="particleGradient" cx="30%" cy="30%" r="70%">
            <Stop offset="0%" stopColor="#FFFFFF" stopOpacity="0.8" />
            <Stop offset="40%" stopColor={foodParticle.color} stopOpacity="1" />
            <Stop offset="100%" stopColor={foodParticle.color} stopOpacity="0.8" />
          </RadialGradient>
        </Defs>

        {/* Main circle */}
        <Circle
          cx={particleSize / 2}
          cy={particleSize / 2}
          r={foodParticle.radius}
          fill="url(#particleGradient)"
          stroke="rgba(255, 255, 255, 0.3)"
          strokeWidth={0.5}
        />

        {/* Highlight */}
        <Circle
          cx={particleSize / 2 - foodParticle.radius * 0.3}
          cy={particleSize / 2 - foodParticle.radius * 0.3}
          r={foodParticle.radius * 0.3}
          fill="rgba(255, 255, 255, 0.6)"
        />
      </Svg>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
  },
  glowContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -50 }, { translateY: -50 }],
  },
  glowSvg: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -50 }, { translateY: -50 }],
  },
  svg: {
    overflow: 'visible',
  },
});

export default FoodParticleEntity;
