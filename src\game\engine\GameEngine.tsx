import React, { useRef, useEffect, useCallback } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { GameEngine as RNGameEngine } from 'react-native-game-engine';
import { useGameStore } from '../../store/gameStore';
import { JoystickInput, Fish, FoodParticle } from '../../types';
import { GAME_CONFIG, PHYSICS } from '../../utils/constants';
import MovementSystem from '../systems/MovementSystem';
import CollisionSystem from '../systems/CollisionSystem';
import SpawningSystem from '../systems/SpawningSystem';
import CameraSystem from '../systems/CameraSystem';
import FishEntity from '../entities/Fish';
import FoodParticleEntity from '../entities/FoodParticle';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface Props {
  joystickInput: JoystickInput;
  onGameOver: (score: number) => void;
}

const GameEngine: React.FC<Props> = ({ joystickInput, onGameOver }) => {
  const gameEngineRef = useRef<any>(null);
  const lastUpdateTime = useRef<number>(Date.now());

  const {
    world,
    isPlaying,
    isPaused,
    currentPlayerId,
    getPlayerFish,
    updatePlayerFish,
    addFish,
    removeFish,
    addFoodParticle,
    removeFoodParticle,
    eatFish,
    eatFood,
    updateFishGrowth,
  } = useGameStore();

  // Initialize game systems
  const movementSystem = useRef(new MovementSystem()).current;
  const collisionSystem = useRef(new CollisionSystem()).current;
  const spawningSystem = useRef(new SpawningSystem()).current;
  const cameraSystem = useRef(new CameraSystem()).current;

  // Game loop update function
  const updateGame = useCallback((entities: any, { time }: { time: { current: number } }) => {
    if (!isPlaying || isPaused) return entities;

    const currentTime = time.current;
    const deltaTime = (currentTime - lastUpdateTime.current) / 1000; // Convert to seconds
    lastUpdateTime.current = currentTime;

    // Get current player fish
    const playerFish = getPlayerFish();
    if (!playerFish) return entities;

    // Update player movement based on joystick input
    if (joystickInput.magnitude > 0) {
      const speed = joystickInput.isBoosting
        ? GAME_CONFIG.player.baseSpeed * GAME_CONFIG.boost.speedMultiplier
        : GAME_CONFIG.player.baseSpeed;

      const newVelocity = {
        x: joystickInput.x * speed,
        y: joystickInput.y * speed,
      };

      updatePlayerFish(playerFish.id, {
        velocity: newVelocity,
        isBoosting: joystickInput.isBoosting,
      });

      // Consume growth progress when boosting
      if (joystickInput.isBoosting) {
        const progressLoss = GAME_CONFIG.boost.progressConsumptionRate * deltaTime;
        updateFishGrowth(playerFish.id, -progressLoss);
      }
    } else {
      // Apply friction when not moving
      updatePlayerFish(playerFish.id, {
        velocity: {
          x: playerFish.velocity.x * PHYSICS.FRICTION,
          y: playerFish.velocity.y * PHYSICS.FRICTION,
        },
        isBoosting: false,
      });
    }

    // Update all systems
    movementSystem.update(world.fishes, deltaTime, world.width, world.height);
    collisionSystem.update(world.fishes, world.foodParticles, {
      onFishEaten: eatFish,
      onFoodEaten: eatFood,
      onPlayerDeath: (fishId: string) => {
        const fish = world.fishes.find(f => f.id === fishId);
        if (fish && fish.playerId === currentPlayerId) {
          onGameOver(fish.score);
        }
      },
    });
    spawningSystem.update(world, addFish, addFoodParticle, deltaTime);
    cameraSystem.update(world.camera, playerFish, screenWidth, screenHeight);

    return entities;
  }, [
    isPlaying,
    isPaused,
    joystickInput,
    world,
    currentPlayerId,
    getPlayerFish,
    updatePlayerFish,
    updateFishGrowth,
    eatFish,
    eatFood,
    onGameOver,
    addFish,
    addFoodParticle,
  ]);

  // Create entities for rendering
  const createEntities = useCallback(() => {
    const entities: any = {};

    // Add fish entities
    world.fishes.forEach((fish) => {
      entities[`fish_${fish.id}`] = {
        fish,
        renderer: FishEntity,
      };
    });

    // Add food particle entities
    world.foodParticles.forEach((food) => {
      entities[`food_${food.id}`] = {
        foodParticle: food,
        renderer: FoodParticleEntity,
      };
    });

    return entities;
  }, [world.fishes, world.foodParticles]);

  // Initialize entities when game starts
  useEffect(() => {
    if (isPlaying && gameEngineRef.current) {
      const entities = createEntities();
      gameEngineRef.current.swap(entities);
    }
  }, [isPlaying, createEntities]);

  // Update entities when world changes
  useEffect(() => {
    if (gameEngineRef.current && isPlaying) {
      const entities = createEntities();
      gameEngineRef.current.swap(entities);
    }
  }, [world.fishes.length, world.foodParticles.length, createEntities, isPlaying]);

  if (!isPlaying) {
    return null;
  }

  return (
    <View style={styles.container}>
      <RNGameEngine
        ref={gameEngineRef}
        style={styles.gameEngine}
        systems={[updateGame]}
        entities={createEntities()}
        running={isPlaying && !isPaused}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0F172A',
  },
  gameEngine: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
});

export default GameEngine;
