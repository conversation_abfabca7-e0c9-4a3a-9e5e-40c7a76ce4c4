import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { View, Text, StyleSheet } from 'react-native';

import { RootStackParamList } from './types';

const Stack = createStackNavigator<RootStackParamList>();

// Simple test screen
const TestScreen: React.FC = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Fish Eat Fish 2</Text>
      <Text style={styles.subtitle}>Navigation Test</Text>
    </View>
  );
};

const App: React.FC = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="MainMenu"
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen
          name="MainMenu"
          component={TestScreen}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1e40af',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 18,
    color: '#e5e7eb',
  },
});

export default App;
