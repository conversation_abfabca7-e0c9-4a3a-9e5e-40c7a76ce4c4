import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ScrollView,
  ImageBackground,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import { COLORS } from '../utils/constants';

type TutorialScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Tutorial'>;

interface Props {
  navigation: TutorialScreenNavigationProp;
}

const { width, height } = Dimensions.get('window');

interface TutorialStep {
  title: string;
  description: string;
  tips: string[];
}

const tutorialSteps: TutorialStep[] = [
  {
    title: 'Movement',
    description: 'Control your fish using the virtual joystick on the screen.',
    tips: [
      'Drag the joystick to move your fish in any direction',
      'Your fish moves primarily horizontally for better gameplay',
      'Movement has inertia for a smoother feel',
    ],
  },
  {
    title: 'Eating & Growing',
    description: 'Eat smaller fish and food particles to grow larger.',
    tips: [
      'You can only eat fish that are strictly smaller than you',
      'Food particles provide small growth boosts',
      'Fill your growth bar to level up and increase in size',
      'Each level requires more food to advance',
    ],
  },
  {
    title: 'Boost Ability',
    description: 'Activate boost for temporary speed increase.',
    tips: [
      'Extend the joystick to maximum range to activate boost',
      'Boosting consumes your growth progress',
      'If you use all progress, you\'ll shrink to the previous level',
      'Use boost strategically to escape or catch prey',
    ],
  },
  {
    title: 'Visual Indicators',
    description: 'Fish are outlined with colors to show their threat level.',
    tips: [
      'Green outline: Fish you can eat (smaller than you)',
      'Red outline: Dangerous fish (larger than you)',
      'Yellow outline: Similar size fish (no eating occurs)',
      'No outline: Fish outside your vision range',
    ],
  },
  {
    title: 'Survival',
    description: 'Avoid larger fish to stay alive.',
    tips: [
      'Larger fish can eat you instantly on contact',
      'Fish of the same size will bounce off each other',
      'Use the minimap to see nearby threats and food',
      'Stay aware of your surroundings',
    ],
  },
  {
    title: 'Multiplayer',
    description: 'Compete with other players in real-time.',
    tips: [
      'Other players appear as fish in the world',
      'Climb the leaderboard by growing larger',
      'Your score increases when you eat other fish',
      'Work your way up from small fish to ocean predator',
    ],
  },
];

const TutorialScreen: React.FC<Props> = ({ navigation }) => {
  const [currentStep, setCurrentStep] = useState(0);

  const handleNext = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      navigation.goBack();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    navigation.goBack();
  };

  const step = tutorialSteps[currentStep];

  return (
    <View style={styles.container}>
      <ImageBackground
        source={{ uri: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojMUU0MEFGO3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiMwRjE3MkE7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiIC8+Cjwvc3ZnPgo=' }}
        style={styles.background}
        resizeMode="cover"
      >
        <View style={styles.overlay}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>How to Play</Text>
            <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
              <Text style={styles.skipButtonText}>Skip</Text>
            </TouchableOpacity>
          </View>

          {/* Progress Indicator */}
          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  { width: `${((currentStep + 1) / tutorialSteps.length) * 100}%` },
                ]}
              />
            </View>
            <Text style={styles.progressText}>
              {currentStep + 1} of {tutorialSteps.length}
            </Text>
          </View>

          {/* Content */}
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <View style={styles.stepContainer}>
              <Text style={styles.stepTitle}>{step.title}</Text>
              <Text style={styles.stepDescription}>{step.description}</Text>

              <View style={styles.tipsContainer}>
                {step.tips.map((tip, index) => (
                  <View key={index} style={styles.tipItem}>
                    <View style={styles.tipBullet} />
                    <Text style={styles.tipText}>{tip}</Text>
                  </View>
                ))}
              </View>
            </View>
          </ScrollView>

          {/* Navigation */}
          <View style={styles.navigation}>
            <TouchableOpacity
              style={[styles.navButton, currentStep === 0 && styles.navButtonDisabled]}
              onPress={handlePrevious}
              disabled={currentStep === 0}
            >
              <Text
                style={[
                  styles.navButtonText,
                  currentStep === 0 && styles.navButtonTextDisabled,
                ]}
              >
                Previous
              </Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.primaryNavButton} onPress={handleNext}>
              <Text style={styles.primaryNavButtonText}>
                {currentStep === tutorialSteps.length - 1 ? 'Got it!' : 'Next'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ImageBackground>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  skipButtonText: {
    fontSize: 16,
    color: '#E5E7EB',
  },
  progressContainer: {
    marginBottom: 30,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.PLAYER,
    borderRadius: 2,
  },
  progressText: {
    fontSize: 14,
    color: '#E5E7EB',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },
  stepContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
  },
  stepTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 18,
    color: '#E5E7EB',
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  tipsContainer: {
    gap: 12,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  tipBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: COLORS.PLAYER,
    marginTop: 8,
  },
  tipText: {
    flex: 1,
    fontSize: 16,
    color: '#E5E7EB',
    lineHeight: 22,
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
    marginTop: 20,
  },
  navButton: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  navButtonDisabled: {
    opacity: 0.5,
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  navButtonTextDisabled: {
    color: '#9CA3AF',
  },
  primaryNavButton: {
    flex: 1,
    backgroundColor: COLORS.PLAYER,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  primaryNavButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
});

export default TutorialScreen;
