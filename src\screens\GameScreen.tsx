import React, { useEffect, useCallback, useState } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  ImageBackground,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useFocusEffect } from '@react-navigation/native';
import { RootStackParamList, JoystickInput, MinimapData } from '../types';
import { useGameStore } from '../store/gameStore';
import { COLORS, GAME_CONFIG } from '../utils/constants';
import GameEngine from '../game/engine/GameEngine';
import VirtualJoystick from '../components/VirtualJoystick';
import ProgressBar from '../components/ProgressBar';
import Leaderboard from '../components/Leaderboard';
import Minimap from '../components/Minimap';

type GameScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Game'>;

interface Props {
  navigation: GameScreenNavigationProp;
}

const { width, height } = Dimensions.get('window');

const GameScreen: React.FC<Props> = ({ navigation }) => {
  const [joystickInput, setJoystickInput] = useState<JoystickInput>({
    x: 0,
    y: 0,
    magnitude: 0,
    isBoosting: false,
  });

  const {
    isPlaying,
    startGame,
    endGame,
    world,
    leaderboard,
    currentPlayerId,
    getPlayerFish,
    updateJoystickInput,
  } = useGameStore();

  // Start game when screen is focused
  useFocusEffect(
    useCallback(() => {
      if (!isPlaying) {
        startGame('Player', 'single'); // TODO: Get from navigation params
      }

      return () => {
        // Cleanup when leaving screen
      };
    }, [isPlaying, startGame])
  );

  // Handle joystick input changes
  const handleJoystickInput = useCallback((input: JoystickInput) => {
    setJoystickInput(input);
    updateJoystickInput(input);
  }, [updateJoystickInput]);

  // Handle game over
  const handleGameOver = useCallback((finalScore: number) => {
    endGame();
    navigation.navigate('GameOver', { finalScore });
  }, [endGame, navigation]);

  // Get player fish data for UI
  const playerFish = getPlayerFish();

  // Create minimap data
  const minimapData: MinimapData = {
    playerPosition: playerFish?.position || { x: 0, y: 0 },
    worldBounds: { width: world.width, height: world.height },
    visibleEntities: [
      // Add visible fish
      ...world.fishes
        .filter(fish => fish.id !== playerFish?.id)
        .map(fish => ({
          id: fish.id,
          position: fish.position,
          type: (fish.isPlayer ? 'player' : 'enemy') as 'player' | 'enemy',
          color: fish.color,
        })),
      // Add visible food
      ...world.foodParticles.map(food => ({
        id: food.id,
        position: food.position,
        type: 'food' as 'food',
        color: food.color,
      })),
    ],
    visionRadius: GAME_CONFIG.vision.radius,
  };

  if (!isPlaying || !playerFish) {
    return (
      <View style={styles.loadingContainer}>
        {/* Loading state */}
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Background */}
      <ImageBackground
        source={{
          uri: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxyYWRpYWxHcmFkaWVudCBpZD0iZ3JhZGllbnQiIGN4PSI1MCUiIGN5PSI1MCUiIHI9IjUwJSI+CiAgICAgIDxzdG9wIG9mZnNldD0iMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiMxRTQwQUY7c3RvcC1vcGFjaXR5OjAuOCIgLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojMEYxNzJBO3N0b3Atb3BhY2l0eToxIiAvPgogICAgPC9yYWRpYWxHcmFkaWVudD4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiIGZpbGw9InVybCgjZ3JhZGllbnQpIiAvPgo8L3N2Zz4K'
        }}
        style={styles.background}
        resizeMode="cover"
      >
        {/* Game Engine */}
        <GameEngine
          joystickInput={joystickInput}
          onGameOver={handleGameOver}
        />

        {/* UI Overlays */}
        <View style={styles.uiContainer}>
          {/* Minimap */}
          <Minimap minimapData={minimapData} />

          {/* Leaderboard */}
          <Leaderboard
            leaderboard={leaderboard}
            currentPlayerId={currentPlayerId}
          />

          {/* Progress Bar */}
          <ProgressBar
            progress={playerFish.growthProgress}
            level={playerFish.sizeLevel}
            maxProgress={playerFish.fishNeededToGrow}
          />

          {/* Virtual Joystick */}
          <VirtualJoystick onInputChange={handleJoystickInput} />
        </View>
      </ImageBackground>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
    justifyContent: 'center',
    alignItems: 'center',
  },
  uiContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'box-none', // Allow touches to pass through to game engine
  },
});

export default GameScreen;
