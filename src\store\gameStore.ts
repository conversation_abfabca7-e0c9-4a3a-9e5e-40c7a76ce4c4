import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import {
  GameState,
  Fish,
  FoodParticle,
  Player,
  LeaderboardEntry,
  JoystickInput,
  Position,
  GameWorld,
  Camera
} from '../types';
import { GAME_CONFIG, FISH_SIZE_LEVELS } from '../utils/constants';
import { generateId } from '../utils/math';

interface GameStore extends GameState {
  // Game world
  world: GameWorld;

  // Input
  joystickInput: JoystickInput;

  // Actions
  startGame: (playerName: string, gameMode: 'single' | 'multiplayer') => void;
  endGame: () => void;
  pauseGame: () => void;
  resumeGame: () => void;
  updateJoystickInput: (input: JoystickInput) => void;
  updatePlayerFish: (fishId: string, updates: Partial<Fish>) => void;
  addFish: (fish: Fish) => void;
  removeFish: (fishId: string) => void;
  addFoodParticle: (food: FoodParticle) => void;
  removeFoodParticle: (foodId: string) => void;
  updateLeaderboard: (leaderboard: LeaderboardEntry[]) => void;
  setConnectionStatus: (status: 'connected' | 'disconnected' | 'connecting') => void;

  // Game logic helpers
  getPlayerFish: () => Fish | undefined;
  getFishByPlayerId: (playerId: string) => Fish | undefined;
  canEatFish: (predatorId: string, preyId: string) => boolean;
  eatFish: (predatorId: string, preyId: string) => void;
  eatFood: (fishId: string, foodId: string) => void;
  levelUpFish: (fishId: string) => void;
  updateFishGrowth: (fishId: string, growthAmount: number) => void;
}

const initialGameWorld: GameWorld = {
  width: GAME_CONFIG.world.width,
  height: GAME_CONFIG.world.height,
  fishes: [],
  foodParticles: [],
  camera: {
    position: { x: 0, y: 0 },
    zoom: 1,
  },
};

const initialJoystickInput: JoystickInput = {
  x: 0,
  y: 0,
  magnitude: 0,
  isBoosting: false,
};

export const useGameStore = create<GameStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    isPlaying: false,
    isPaused: false,
    gameMode: 'single',
    players: [],
    currentPlayerId: '',
    leaderboard: [],
    connectionStatus: 'disconnected',
    world: initialGameWorld,
    joystickInput: initialJoystickInput,

    // Actions
    startGame: (playerName: string, gameMode: 'single' | 'multiplayer') => {
      const playerId = generateId();
      const fishId = generateId();

      // Create player fish
      const playerFish: Fish = {
        id: fishId,
        position: {
          x: GAME_CONFIG.world.width / 2,
          y: GAME_CONFIG.world.height / 2
        },
        velocity: { x: 0, y: 0 },
        sizeLevel: 1,
        radius: FISH_SIZE_LEVELS[0].radius,
        color: FISH_SIZE_LEVELS[0].color,
        isPlayer: true,
        playerId,
        playerName,
        score: 0,
        growthProgress: 0,
        fishNeededToGrow: FISH_SIZE_LEVELS[0].fishNeeded,
        isBoosting: false,
        isAlive: true,
        lastUpdate: Date.now(),
      };

      const player: Player = {
        id: playerId,
        name: playerName,
        fishId,
        score: 0,
        isConnected: true,
        lastSeen: Date.now(),
      };

      set({
        isPlaying: true,
        isPaused: false,
        gameMode,
        currentPlayerId: playerId,
        players: [player],
        world: {
          ...get().world,
          fishes: [playerFish],
          camera: {
            ...get().world.camera,
            target: fishId,
          },
        },
        leaderboard: [{
          playerId,
          playerName,
          score: 0,
          rank: 1,
        }],
      });
    },

    endGame: () => {
      set({
        isPlaying: false,
        isPaused: false,
        world: {
          ...initialGameWorld,
          fishes: [],
          foodParticles: [],
        },
        joystickInput: initialJoystickInput,
      });
    },

    pauseGame: () => {
      set({ isPaused: true });
    },

    resumeGame: () => {
      set({ isPaused: false });
    },

    updateJoystickInput: (input: JoystickInput) => {
      set({ joystickInput: input });
    },

    updatePlayerFish: (fishId: string, updates: Partial<Fish>) => {
      set((state) => ({
        world: {
          ...state.world,
          fishes: state.world.fishes.map((fish) =>
            fish.id === fishId ? { ...fish, ...updates } : fish
          ),
        },
      }));
    },

    addFish: (fish: Fish) => {
      set((state) => ({
        world: {
          ...state.world,
          fishes: [...state.world.fishes, fish],
        },
      }));
    },

    removeFish: (fishId: string) => {
      set((state) => ({
        world: {
          ...state.world,
          fishes: state.world.fishes.filter((fish) => fish.id !== fishId),
        },
      }));
    },

    addFoodParticle: (food: FoodParticle) => {
      set((state) => ({
        world: {
          ...state.world,
          foodParticles: [...state.world.foodParticles, food],
        },
      }));
    },

    removeFoodParticle: (foodId: string) => {
      set((state) => ({
        world: {
          ...state.world,
          foodParticles: state.world.foodParticles.filter((food) => food.id !== foodId),
        },
      }));
    },

    updateLeaderboard: (leaderboard: LeaderboardEntry[]) => {
      set({ leaderboard });
    },

    setConnectionStatus: (status: 'connected' | 'disconnected' | 'connecting') => {
      set({ connectionStatus: status });
    },

    // Helper functions
    getPlayerFish: () => {
      const state = get();
      return state.world.fishes.find((fish) => fish.playerId === state.currentPlayerId);
    },

    getFishByPlayerId: (playerId: string) => {
      const state = get();
      return state.world.fishes.find((fish) => fish.playerId === playerId);
    },

    canEatFish: (predatorId: string, preyId: string) => {
      const state = get();
      const predator = state.world.fishes.find((f) => f.id === predatorId);
      const prey = state.world.fishes.find((f) => f.id === preyId);

      if (!predator || !prey) return false;
      return predator.sizeLevel > prey.sizeLevel;
    },

    eatFish: (predatorId: string, preyId: string) => {
      const state = get();
      const predator = state.world.fishes.find((f) => f.id === predatorId);
      const prey = state.world.fishes.find((f) => f.id === preyId);

      if (!predator || !prey || !get().canEatFish(predatorId, preyId)) return;

      // Calculate score and growth
      const scoreGain = prey.sizeLevel * 10;
      const growthGain = 1.0;

      // Update predator
      get().updatePlayerFish(predatorId, {
        score: predator.score + scoreGain,
        growthProgress: predator.growthProgress + growthGain,
      });

      // Remove prey
      get().removeFish(preyId);

      // Check for level up
      if (predator.growthProgress + growthGain >= predator.fishNeededToGrow) {
        get().levelUpFish(predatorId);
      }
    },

    eatFood: (fishId: string, foodId: string) => {
      const state = get();
      const fish = state.world.fishes.find((f) => f.id === fishId);
      const food = state.world.foodParticles.find((f) => f.id === foodId);

      if (!fish || !food) return;

      const growthGain = food.nutritionValue * 0.1;

      get().updatePlayerFish(fishId, {
        growthProgress: fish.growthProgress + growthGain,
      });

      get().removeFoodParticle(foodId);

      // Check for level up
      if (fish.growthProgress + growthGain >= fish.fishNeededToGrow) {
        get().levelUpFish(fishId);
      }
    },

    levelUpFish: (fishId: string) => {
      const state = get();
      const fish = state.world.fishes.find((f) => f.id === fishId);

      if (!fish || fish.sizeLevel >= FISH_SIZE_LEVELS.length) return;

      const newLevel = Math.min(fish.sizeLevel + 1, FISH_SIZE_LEVELS.length);
      const levelConfig = FISH_SIZE_LEVELS[newLevel - 1];

      get().updatePlayerFish(fishId, {
        sizeLevel: newLevel,
        radius: levelConfig.radius,
        color: levelConfig.color,
        growthProgress: 0,
        fishNeededToGrow: levelConfig.fishNeeded,
      });
    },

    updateFishGrowth: (fishId: string, growthAmount: number) => {
      const state = get();
      const fish = state.world.fishes.find((f) => f.id === fishId);

      if (!fish) return;

      const newProgress = Math.max(0, fish.growthProgress + growthAmount);

      // If growth goes negative and we're not at level 1, level down
      if (newProgress <= 0 && fish.sizeLevel > 1) {
        const newLevel = fish.sizeLevel - 1;
        const levelConfig = FISH_SIZE_LEVELS[newLevel - 1];

        get().updatePlayerFish(fishId, {
          sizeLevel: newLevel,
          radius: levelConfig.radius,
          color: levelConfig.color,
          growthProgress: levelConfig.fishNeeded * 0.5, // Start at half progress of previous level
          fishNeededToGrow: levelConfig.fishNeeded,
        });
      } else {
        get().updatePlayerFish(fishId, {
          growthProgress: newProgress,
        });

        // Check for level up
        if (newProgress >= fish.fishNeededToGrow) {
          get().levelUpFish(fishId);
        }
      }
    },
  }))
);
