import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { <PERSON>, Ellipse, Defs, Mask } from 'react-native-svg';
import { MinimapData, Position, Size } from '../types';
import { UI_CONSTANTS, COLORS } from '../utils/constants';

interface Props {
  minimapData: MinimapData;
  style?: any;
}

const Minimap: React.FC<Props> = ({ minimapData, style }) => {
  const { playerPosition, worldBounds, visibleEntities, visionRadius } = minimapData;

  const mapSize = UI_CONSTANTS.MINIMAP_SIZE;
  const scaleX = mapSize / worldBounds.width;
  const scaleY = mapSize / worldBounds.height;

  // Convert world coordinates to minimap coordinates
  const worldToMinimap = (worldPos: Position): Position => ({
    x: worldPos.x * scaleX,
    y: worldPos.y * scaleY,
  });

  const playerMinimapPos = worldToMinimap(playerPosition);
  const visionRadiusScaled = visionRadius * Math.min(scaleX, scaleY);

  const getEntityColor = (type: string): string => {
    switch (type) {
      case 'player':
        return COLORS.PLAYER;
      case 'enemy':
        return COLORS.ENEMY;
      case 'food':
        return COLORS.FOOD;
      default:
        return '#FFFFFF';
    }
  };

  const getEntitySize = (type: string): number => {
    switch (type) {
      case 'player':
        return 4;
      case 'enemy':
        return 3;
      case 'food':
        return 2;
      default:
        return 2;
    }
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.minimapContainer}>
        <Svg width={mapSize} height={mapSize} style={styles.svg}>
          <Defs>
            <Mask id="visionMask">
              <Circle
                cx={playerMinimapPos.x}
                cy={playerMinimapPos.y}
                r={visionRadiusScaled}
                fill="white"
              />
            </Mask>
          </Defs>

          {/* World boundary */}
          <Circle
            cx={mapSize / 2}
            cy={mapSize / 2}
            r={mapSize / 2 - 2}
            fill="rgba(30, 64, 175, 0.3)"
            stroke="rgba(255, 255, 255, 0.4)"
            strokeWidth={2}
          />

          {/* Vision area */}
          <Ellipse
            cx={playerMinimapPos.x}
            cy={playerMinimapPos.y}
            rx={visionRadiusScaled}
            ry={visionRadiusScaled * 0.8} // Slightly elliptical to match screen aspect
            fill="rgba(16, 185, 129, 0.1)"
            stroke="rgba(16, 185, 129, 0.3)"
            strokeWidth={1}
          />

          {/* Visible entities */}
          {visibleEntities.map((entity) => {
            const entityPos = worldToMinimap(entity.position);
            const entitySize = getEntitySize(entity.type);
            const entityColor = entity.color || getEntityColor(entity.type);

            return (
              <Circle
                key={entity.id}
                cx={entityPos.x}
                cy={entityPos.y}
                r={entitySize}
                fill={entityColor}
                stroke="rgba(0, 0, 0, 0.5)"
                strokeWidth={0.5}
                mask="url(#visionMask)"
              />
            );
          })}

          {/* Player indicator */}
          <Circle
            cx={playerMinimapPos.x}
            cy={playerMinimapPos.y}
            r={5}
            fill={COLORS.PLAYER}
            stroke="#FFFFFF"
            strokeWidth={2}
          />

          {/* Player direction indicator (small arrow) */}
          <Circle
            cx={playerMinimapPos.x}
            cy={playerMinimapPos.y - 8}
            r={2}
            fill="#FFFFFF"
          />
        </Svg>

        {/* Border overlay */}
        <View style={styles.border} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 40,
    left: 20,
    width: UI_CONSTANTS.MINIMAP_SIZE,
    height: UI_CONSTANTS.MINIMAP_SIZE,
  },
  minimapContainer: {
    width: UI_CONSTANTS.MINIMAP_SIZE,
    height: UI_CONSTANTS.MINIMAP_SIZE,
    borderRadius: UI_CONSTANTS.MINIMAP_SIZE / 2,
    backgroundColor: COLORS.UI_OVERLAY,
    overflow: 'hidden',
    position: 'relative',
  },
  svg: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  border: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: UI_CONSTANTS.MINIMAP_SIZE,
    height: UI_CONSTANTS.MINIMAP_SIZE,
    borderRadius: UI_CONSTANTS.MINIMAP_SIZE / 2,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.4)',
  },
});

export default Minimap;
